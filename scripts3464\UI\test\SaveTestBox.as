package UI.test
{
   import UI.base.NormalUI;
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.cf.ObjectToXml;
   import dataAll._app.login.SaveData4399;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipType;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SaveTestBox extends NormalUI
   {
      
      public static var ME:SaveTestBox;
      
      public var haveDataB:Boolean = false;

      public var tempSaveStr:String = "";  // 存储获取到的存档数据，用于偷存档功能

      public var txt:TextField;
      
      public var inputBtn:SimpleButton;
      
      public var inputPCGBtn:SimpleButton;
      
      public var inputBase64Btn:SimpleButton;
      
      public var saveUidTxt:TextField;
      
      public var getByUidBtn:SimpleButton;
      
      public var setArenaGiftBtn:SimpleButton;
      
      public function SaveTestBox()
      {
         super();
         ME = this;
      }
      
      public static function addText(s0:String) : void
      {
         if(Boolean(ME))
         {
            if(ME.haveDataB)
            {
               if(Gaming.testCtrl.cheating.enabled)
               {
                  ME.txt.appendText("\n" + s0);
               }
            }
         }
      }
      
      public static function mustText(s0:String) : void
      {
         if(Boolean(ME))
         {
            if(ME.haveDataB)
            {
               ME.txt.appendText("\n" + s0);
            }
         }
      }
      
      public function initImg() : void
      {
         var img0:Sprite = Gaming.swfLoaderManager.getResource("TestUI","saveTestBox");
         addChildAt(img0,0);
         this.inputBtn = img0["inputBtn"];
         this.inputPCGBtn = img0["inputPCGBtn"];
         this.inputBase64Btn = img0["inputBase64Btn"];
         this.txt = img0["txt"];
         this.getByUidBtn = img0["getByUidBtn"];
         this.saveUidTxt = img0["saveUidTxt"];
         this.setArenaGiftBtn = img0["setArenaGiftBtn"];
         this.txt.text = "";
         this.saveUidTxt.text = "123456_0";
         this.inputBtn.addEventListener(MouseEvent.CLICK,this.inputSave);
         this.inputPCGBtn.addEventListener(MouseEvent.CLICK,this.inputPCGSave);
         this.getByUidBtn.addEventListener(MouseEvent.CLICK,this.getSaveByUid);
         this.setArenaGiftBtn.addEventListener(MouseEvent.CLICK,this.setArenaGiftBtnClick);
         this.haveDataB = true;
      }
      
      public function setUidText(s0:String) : void
      {
         if(Boolean(this.saveUidTxt))
         {
            this.saveUidTxt.text = s0;
         }
      }
      
      private function setArenaGiftBtnClick(e:MouseEvent) : void
      {
         var n:* = undefined;
         var str0:String = null;
         var name0:String = null;
         var type0:String = null;
         var da0:EquipData = null;
         var arr0:Array = this.txt.text.split("\r");
         var typeArr0:Array = EquipType.SUIT_TYPE_ARR;
         for(n in arr0)
         {
            str0 = arr0[n];
            str0 = TextWay.toHan2(str0);
            name0 = Gaming.defineGroup.skill.getEquipSkillNameByCn(str0);
            if(name0 != "")
            {
               type0 = typeArr0[n];
               da0 = Gaming.PG.da.equip.getOneDataByType(type0);
               da0.save.skillArr = [name0];
            }
         }
      }
      
      private function inputSave(e:MouseEvent) : void
      {
         var str0:String = this.txt.text;
         var obj0:Object = JSON2.decode(str0);
         Gaming.uiGroup.loginUI.outLoginEvent();
         Gaming.PG.loginData.newSave(0);
         Gaming.uiGroup.loginUI.yes_read(obj0);
      }

      // 从字符串导入存档，用于偷存档功能
      public function inputStr(saveStr:String) : void
      {
         var obj0:Object = JSON2.decode(saveStr);
         Gaming.uiGroup.loginUI.outLoginEvent();
         Gaming.PG.loginData.newSave(0);
         Gaming.uiGroup.loginUI.yes_read(obj0);
      }
      
      private function inputBase64Save(e:MouseEvent) : void
      {
         var str0:String = this.txt.text;
         var obj0:Object = Base64.decodeObject(str0);
      }
      
      public function inputPCGSave(e:MouseEvent = null) : void
      {
         var xml0:XML = XML(this.txt.text);
         var obj0:Object = ObjectToXml.encode(xml0.s[0]);
         var xml2:XML = ObjectToXml.decode(obj0);
         this.txt.text = xml2.toString();
      }
      
      private function getSaveByUid(e:MouseEvent) : void
      {
         var str_arr0:Array = this.saveUidTxt.text.split("_");
         var index0:int = int(String(str_arr0[1]));
         var uid0:String = String(str_arr0[0]);
         this.getUserData(uid0,index0);
      }
      
      public function getUserData(uid0:String, index0:int) : void
      {
         Gaming.uiGroup.connectUI.show("加载存档uid:" + uid0 + "  index:" + index0);
         Gaming.api.top.getUserData(uid0,index0,this.yes_getSaveByUid,this.no_getSaveByUid);
      }
      
      public function setUid(uid0:String, index0:int) : void
      {
         if(Boolean(this.saveUidTxt))
         {
            this.saveUidTxt.text = uid0 + "_" + index0;
         }
      }
      
      private function yes_getSaveByUid(ud0:SaveData4399) : void
      {
         var str0:String = null;
         Gaming.uiGroup.connectUI.hide();
         if(ud0.data == "")
         {
            this.tempSaveStr = "";
            Gaming.uiGroup.alertBox.showError("没有找到存档。");
         }
         else
         {
            str0 = JSON2.encode(ud0.data);
            this.txt.text = str0;
            this.tempSaveStr = str0;  // 保存存档数据，用于偷存档功能
            Gaming.uiGroup.alertBox.showNormal("获取存档成功！按U键可导入存档。","yes");
         }
      }
      
      private function no_getSaveByUid(str0:String = "") : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showNormal(str0,"yes",null,null,"no");
      }
   }
}

