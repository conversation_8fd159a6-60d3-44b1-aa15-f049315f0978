package UI.arena.top
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.arena.ArenaTopCtrl;
   import UI.arena.info.ArenaInfoBox;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import UI.base.page.PageBox;
   import UI.top.TopBarBox;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.TopBarDataGroup;
   import dataAll._app.top.define.TopBarDefineGroup;
   import flash.display.Sprite;
   import gameAll.level.arena.ArenaCtrl;
   
   public class ArenaTopBoard extends NormalUI
   {
      
      public var labelBox:LabelBox = new LabelBox();
      
      private var box:TopBarBox = new TopBarBox();
      
      private var pageBox:PageBox = new PageBox();
      
      private var nowDefineG:TopBarDefineGroup;
      
      private var maxBarNum:int = 10;
      
      private var nowPage:int = 0;
      
      private var nowUploadDefine:TopBarDefineGroup;
      
      public var infoBox:ArenaInfoBox;
      
      private var labelTag:Sprite;
      
      private var barTag:Sprite;
      
      private var pageTag:Sprite;
      
      public function ArenaTopBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["labelTag","barTag","pageTag"];
         super.setImg(img0);
         var nameArr0:Array = Gaming.defineGroup.top.getNameArr("arena").reverse();
         var cnNameArr0:Array = Gaming.defineGroup.top.getCnNameArr("arena").reverse();
         this.labelBox.arg.init(10,1,1,0);
         this.labelBox.inData("ArenaUI/topLabelBtn",nameArr0,cnNameArr0);
         this.labelBox.setChoose(nameArr0[0]);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.labelBox);
         this.box.initTitle("ArenaUI/topBar",0);
         this.box.arg.init(1,10,0,1);
         this.box.x = this.barTag.x;
         this.box.y = this.barTag.y;
         this.box.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         addChild(this.box);
         this.pageBox.setToNormalBtn();
         addChild(this.pageBox);
         NormalUICtrl.setTag(this.pageBox,this.pageTag);
         this.pageBox.setMaxPageShow(10);
         this.pageBox.setPageNumOut(10);
         this.pageBox.addEventListener(ClickEvent.ON_SHOW_PAGE,this.pageClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         if(label0 == "")
         {
            label0 = this.labelBox.getFirstLabel();
         }
         this.labelBox.setChoose(label0);
         this.start(label0);
      }
      
      override public function show() : void
      {
         super.show();
         var bb0:Boolean = UIOrder.zuobiPan();
         if(bb0)
         {
            Gaming.uiGroup.arenaUI.hide();
            return;
         }
         this.uploadScore();
      }
      
      private function uploadScore() : void
      {
         this.nowUploadDefine = ArenaTopCtrl.getNowTopBarDefineGroup();
         ArenaTopCtrl.uploadScoreBy(this.yes_unloadScore,this.no_unloadScore);
      }
      
      private function yes_unloadScore(returnDataObj:Object) : void
      {
         this.showBox(this.nowUploadDefine.name);
      }
      
      private function no_unloadScore(str0:String = "") : void
      {
         this.showBox(this.nowUploadDefine.name);
      }
      
      private function start(type0:String) : void
      {
         this.nowPage = 0;
         this.nowDefineG = Gaming.defineGroup.top.getDefine(type0);
         this.showPage(this.nowPage);
      }
      
      private function getRankList(page0:int) : void
      {
         Gaming.uiGroup.connectUI.show("获取排行榜数据……");
         Gaming.api.top.getRankListsData(this.nowDefineG.id,this.maxBarNum,page0 + 1,this.yes_getRankList,this.no_getRankList);
      }
      
      private function yes_getRankList(dataArr0:Array) : void
      {
         var dg0:TopBarDataGroup = new TopBarDataGroup();
         dg0.inData_byArr(dataArr0,this.nowDefineG.name);
         this.box.inData_byTop(dg0);
         Gaming.uiGroup.connectUI.hide();
      }
      
      private function no_getRankList(str0:String = "") : void
      {
         Gaming.uiGroup.alertBox.showNormal("获取排行榜数据失败！\n" + str0,"yes",null,null,"no");
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.arenaUI.hide();
      }
      
      public function showPage(num0:int) : void
      {
         this.pageBox.showPage(num0);
      }
      
      private function pageClick(e:ClickEvent) : void
      {
         this.getRankList(e.index);
      }
      
      private function barClick(e:ClickEvent) : void
      {
         var da0:TopBarData = e.childData as TopBarData;
         Gaming.uiGroup.testUI.saveBox.setUid(da0.uid,da0.index);

         // 自动获取该玩家的存档数据，用于偷存档功能
         Gaming.uiGroup.testUI.saveBox.getUserData(da0.uid, da0.index);

         if(!da0.hideTextB())
         {
            ArenaCtrl.chooseArivalByTopBarData(da0);
         }
      }
   }
}

