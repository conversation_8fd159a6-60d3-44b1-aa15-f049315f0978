package UI
{
   import com.adobe.serialization.json.JSON2;
   import UI.achieve.AchieveUI;
   import UI.api.count.ShopCountObj;
   import UI.arena.ArenaUI;
   import UI.ask.AskUI;
   import UI.bag.BagUI;
   import UI.bag.ItemsGripBtnListCtrl;
   import UI.bag.ItemsGripMoveCtrl;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.WearUI;
   import UI.bag.allBag.AllBagUI;
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.alert.AlertBox;
   import UI.base.armsFilter.ArmsFilterBoard;
   import UI.base.armsSkin.ArmsSkinBox;
   import UI.base.btnList.BtnList;
   import UI.base.bulletPath.BulletPathBox;
   import UI.base.cheating.StopHandUpBox;
   import UI.base.drag.ItemsDragController;
   import UI.base.focusLost.FocusLostBox;
   import UI.base.gift.GiftShowBox;
   import UI.base.gift.GiftTipBox;
   import UI.base.grid.NormalGrid;
   import UI.base.must.NormalExpendBox;
   import UI.base.tip.OneTextGather;
   import UI.base.tip.TipBox;
   import UI.blackMarket.BlackMarketUI;
   import UI.city.CityUI;
   import UI.city.food.FoodUI;
   import UI.count.CountCtrl;
   import UI.count.LevelCountUI;
   import UI.count.beforeDps.DpsCountBox;
   import UI.demon.DemonUI;
   import UI.edit.BosseditUI;
   import UI.edit.card.BcardBattleBox;
   import UI.edit.list.EditListBox;
   import UI.forging.ForgingUI;
   import UI.gameOver.GameOverUI;
   import UI.gameWorld.GameWorldUI;
   import UI.gameWorld.task.GameWorldTaskBox;
   import UI.gift.GiftUI;
   import UI.gift.anniver.AnniverUI;
   import UI.gift.anniver.gm.AnniverGmUI;
   import UI.gift.guoQing.GuoQingUI;
   import UI.gift.holiday.SummerGiftUI;
   import UI.gift.otherBox.SeventhMoonBox;
   import UI.gift.yuanXiao.YuanXiaoUI;
   import UI.gift.zhongQiu.ZhongQiuUI;
   import UI.guide.GuideBox;
   import UI.guide.GuideOrder;
   import UI.head.HeadUI;
   import UI.helper.HelperUI;
   import UI.house.HouseUI;
   import UI.loading.ConnectUI;
   import UI.loading.LoadingUI;
   import UI.login.LoginUI;
   import UI.main.MainUI;
   import UI.main.WorldMapBox;
   import UI.notice.NoticeUI;
   import UI.partner.MoreBox;
   import UI.partner.p1.P1SwapBox;
   import UI.parts.PartsUI;
   import UI.peak.PeakUI;
   import UI.pet.PetUI;
   import UI.post.PostUI;
   import UI.setting.SettingUI;
   import UI.setting.key.SettingKeyBox;
   import UI.shop.ShopUI;
   import UI.skill.SkillUI;
   import UI.space.SpaceUI;
   import UI.sweeping.SweepingBox;
   import UI.task.TaskUI;
   import UI.test.TestUI;
   import UI.test.SaveTestBox;
   import UI.top.TopUI;
   import UI.tower.TowerUI;
   import UI.union.UnionUI;
   import UI.vehicle.VehicleUI;
   import UI.vip.VipUI;
   import UI.wilder.WilderUI;
   import com.sounto.net.SWFLoaderManager;
   import com.sounto.test.Stats;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.setting.key.SettingKeySave;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.things.ThingsProps;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.ui.Keyboard;
   import gameAll.image.GameSprite;
   
   public class UIGroup
   {
      
      public static var mustShowMoreNameArr:Array = ["wear","skill","shop"];
      
      private var now_t:int = 0;
      
      private var heartbeatT:int = 0;
      
      private var swfM:SWFLoaderManager = null;
      
      private var stat:Stats = null;
      
      private var topCover:Sprite = null;
      
      public var loadingUI:LoadingUI = null;
      
      public var connectUI:ConnectUI = new ConnectUI();
      
      public var focusBox:FocusLostBox = new FocusLostBox();
      
      public var dragCtrl:ItemsDragController = new ItemsDragController();
      
      public var tipBox:TipBox = new TipBox();
      
      public var compareTipBox:TipBox = new TipBox();
      
      public var alertBox:AlertBox = new AlertBox();
      
      public var stopHandUpBox:StopHandUpBox = new StopHandUpBox();
      
      public var btnList:BtnList = new BtnList();
      
      public var guideBox:GuideBox = new GuideBox();
      
      public var giftTip:GiftTipBox = new GiftTipBox();
      
      public var giftShowBox:GiftShowBox = new GiftShowBox();
      
      public var mustBox:NormalExpendBox = new NormalExpendBox();
      
      public var bulletPathBox:BulletPathBox = new BulletPathBox();
      
      public var armsSkinBox:ArmsSkinBox = new ArmsSkinBox();
      
      public var bcardBattleBox:BcardBattleBox = new BcardBattleBox();
      
      public var armsFilterBoard:ArmsFilterBoard = new ArmsFilterBoard();
      
      public var p1SwapBox:P1SwapBox = new P1SwapBox();
      
      public var editList:EditListBox = new EditListBox();
      
      public var testUI:TestUI;
      
      public var dpsCountBox:DpsCountBox = new DpsCountBox();
      
      public var gameWorldUI:GameWorldUI = new GameWorldUI();
      
      public var mainUI:MainUI = new MainUI();
      
      public var worldMapBox:WorldMapBox = new WorldMapBox();
      
      public var gameOverUI:GameOverUI = new GameOverUI();
      
      public var taskBox:GameWorldTaskBox;
      
      public var unionTaskBox:GameWorldTaskBox;
      
      public var moreBox:MoreBox = new MoreBox();
      
      public var sweepingBox:SweepingBox = new SweepingBox();
      
      public var appUIObj:Object = {};
      
      public var bagUI:BagUI = new BagUI();
      
      public var allBagUI:AllBagUI = new AllBagUI();
      
      public var wearUI:WearUI = new WearUI();
      
      public var settingUI:SettingUI = new SettingUI();
      
      public var skillUI:SkillUI = new SkillUI();
      
      public var taskUI:TaskUI = new TaskUI();
      
      public var shopUI:ShopUI = new ShopUI();
      
      public var partsUI:PartsUI = new PartsUI();
      
      public var topUI:TopUI = new TopUI();
      
      public var giftUI:GiftUI = new GiftUI();
      
      public var vipUI:VipUI = new VipUI();
      
      public var arenaUI:ArenaUI = new ArenaUI();
      
      public var blackMarketUI:BlackMarketUI = new BlackMarketUI();
      
      public var achieveUI:AchieveUI = new AchieveUI();
      
      public var houseUI:HouseUI = new HouseUI();
      
      public var forgingUI:ForgingUI = new ForgingUI();
      
      public var petUI:PetUI = new PetUI();
      
      public var askUI:AskUI = new AskUI();
      
      public var vehicleUI:VehicleUI = new VehicleUI();
      
      public var unionUI:UnionUI = new UnionUI();
      
      public var headUI:HeadUI = new HeadUI();
      
      public var loginUI:LoginUI = new LoginUI();
      
      public var noticeUI:NoticeUI = new NoticeUI();
      
      public var seventhMoonUI:SeventhMoonBox = new SeventhMoonBox();
      
      public var helperUI:HelperUI = new HelperUI();
      
      public var postUI:PostUI = new PostUI();
      
      public var wilderUI:WilderUI = new WilderUI();
      
      public var peakUI:PeakUI = new PeakUI();
      
      public var cityUI:CityUI = new CityUI();
      
      public var foodUI:FoodUI = new FoodUI();
      
      public var demonUI:DemonUI = new DemonUI();
      
      public var bosseditUI:BosseditUI = new BosseditUI();
      
      public var spaceUI:SpaceUI = new SpaceUI();
      
      public var towerUI:TowerUI = new TowerUI();
      
      public var levelCountUI:LevelCountUI = new LevelCountUI();
      
      public var yuanXiaoUI:YuanXiaoUI = new YuanXiaoUI();
      
      public var anniverUI:AnniverUI = new AnniverUI();
      
      public var summerUI:SummerGiftUI = new SummerGiftUI();
      
      public var anniverGmUI:AnniverGmUI = new AnniverGmUI();
      
      public var guoQingUI:GuoQingUI = new GuoQingUI();
      
      public var zhongQiuUI:ZhongQiuUI = new ZhongQiuUI();
      
      public var noShowBagArr:Array = [this.bagUI,this.skillUI,this.blackMarketUI];
      
      public function UIGroup()
      {
         super();
      }
      
      private static function get gs() : GameSprite
      {
         return Gaming.gameSprite;
      }
      
      public static function setUIMiddle(ui0:DisplayObject, getRectB0:Boolean = true, width0:int = 0, height0:int = 0) : void
      {
         var rect0:Rectangle = null;
         var x0:int = 0;
         var y0:int = 0;
         if(getRectB0)
         {
            rect0 = ui0.getRect(ui0);
            x0 = rect0.x;
            y0 = rect0.y;
         }
         if(width0 == 0)
         {
            width0 = ui0.width;
         }
         if(height0 == 0)
         {
            height0 = ui0.height;
         }
         ui0.x = int(-width0 / 2 - x0 + Gaming.WIDTH / 2);
         ui0.y = int(-height0 / 2 - y0 + Gaming.APP_UI_HEIGHT / 2);
      }
      
      public function init() : *
      {
         this.swfM = Gaming.swfLoaderManager;
         UIShow.UIG = this as UIGroup;
         this.propertyAdd();
         this.resourceAdd();
         this.topShowAdd();
         this.bigShowAdd();
         this.worldMapBox.showMap(WorldMapDefine.INFECTED_AREA);
         this.testUI = new TestUI();
         this.testUI.initImg();
         this.dpsCountBox.setCon(gs.ele);
         this.topCover = this.getBasicMovieClip("cover");
         if(Gaming.isLocal() == false)
         {
            Gaming.ME.addChild(this.topCover);
         }
      }
      
      public function getTopOver() : Sprite
      {
         return this.topCover;
      }
      
      private function propertyAdd() : void
      {
         NormalGrid.pro_arr = ClassProperty.getProArr(new NormalGrid(),false);
         this.worldMapBox.affterDefineInit();
         ShopCountObj.pro_arr = ClassProperty.getProArr(new ShopCountObj());
      }
      
      private function resourceAdd() : void
      {
         OneTextGather.setIconResource(this.getBasicMovieClip("textGatherIcon"));
      }
      
      private function topShowAdd() : void
      {
         this.connectUI.imgInit();
         this.focusBox.imgInit();
         this.tipBox.init();
         this.tipBox.hide();
         this.compareTipBox.init();
         this.compareTipBox.hide();
         this.alertBox.imgInit();
         this.stopHandUpBox.imgInit();
         this.btnList.imgInit();
         this.btnList.hide();
         this.giftShowBox.setImg(this.getBasicMovieClip("giftShowBox"));
         this.giftShowBox.hide();
         this.giftShowBox.x = Gaming.WIDTH / 2;
         this.giftShowBox.y = Gaming.APP_UI_HEIGHT / 2;
         this.noticeUI.setImg(Gaming.ME.noticeUIMc);
         this.noticeUI.UILabel = "notice";
         setUIMiddle(this.noticeUI);
         this.noticeUI.hide();
         this.noticeUI.y = 145;
         this.noticeUI.x = 175;
         this.seventhMoonUI.setImg(this.swfM.getResource("GiftUI","seventhMoonBox"));
         this.seventhMoonUI.UILabel = "seventhMoon";
         setUIMiddle(this.seventhMoonUI);
         this.seventhMoonUI.hide();
         this.seventhMoonUI.setCon(gs.L_UI);
         this.guideBox.setCon(gs.cover);
         this.focusBox.setCon(gs.cover);
         gs.cover.addChild(this.connectUI);
         this.compareTipBox.setCon(gs.tip);
         this.tipBox.setCon(gs.tip);
         this.btnList.setCon(gs.tip);
         gs.ele.addChildAt(this.dragCtrl,0);
         this.alertBox.setCon(gs.ele);
         this.giftShowBox.setCon(gs.ele);
         this.giftTip.setCon(gs.ele);
         ItemsGripTipCtrl.init();
         Gaming.gameSprite.addEventListener(MouseEvent.MOUSE_UP,this.mouseUp);
         ItemsGripBtnListCtrl.init();
      }
      
      private function bigShowAdd() : void
      {
         this.mainUI.setImg(this.swfM.getResource("MainUI","MainUI"));
         this.worldMapBox.setImg(this.swfM.getResource("MainUI","worldMapBox"));
         this.gameWorldUI.setImg(this.swfM.getResource("GameWorldUI","GameWorldUI"));
         this.taskBox = this.gameWorldUI.taskBox;
         this.unionTaskBox = this.gameWorldUI.unionBox;
         this.bagUI.setImg(this.swfM.getResource("BasicUI","bagUI"));
         this.bagUI.UILabel = "bag";
         this.wearUI.setImg(this.swfM.getResource("BasicUI","wearUI"));
         this.wearUI.x = 70;
         this.wearUI.y = 15;
         this.wearUI.UILabel = "wear";
         this.addInAppUIObj(this.wearUI);
         this.bagUI.x = this.wearUI.x + this.wearUI.width;
         this.bagUI.y = this.wearUI.y;
         this.addInAppUIObj(this.bagUI);
         this.houseUI.setImg(this.swfM.getResource("HouseUI","houseUI"));
         this.houseUI.x = 70;
         this.houseUI.y = 15;
         this.houseUI.UILabel = "house";
         this.addInAppUIObj(this.houseUI);
         this.allBagUI.setImg(this.swfM.getResource("BasicUI","allBagUI"));
         this.allBagUI.x = this.wearUI.x + this.wearUI.width;
         this.allBagUI.y = this.wearUI.y;
         this.allBagUI.UILabel = "allBag";
         this.addInAppUIObj(this.allBagUI);
         this.settingUI.setImg(this.swfM.getResource("BasicUI","settingUI"));
         this.settingUI.UILabel = "setting";
         this.addInAppUIObj(this.settingUI,true);
         this.settingUI.y = 25;
         this.skillUI.setImg(this.swfM.getResource("SkillUI","skillUI"));
         this.skillUI.UILabel = "skill";
         this.addInAppUIObj(this.skillUI,true);
         this.taskUI.setImg(this.swfM.getResource("TaskUI","taskUI"));
         this.taskUI.UILabel = "task";
         this.addInAppUIObj(this.taskUI,true);
         this.taskUI.x = 75;
         this.taskUI.y = this.skillUI.y;
         this.shopUI.setImg(this.swfM.getResource("ShopUI","shopUI"));
         this.shopUI.x = this.wearUI.x;
         this.shopUI.y = this.wearUI.y;
         this.shopUI.UILabel = "shop";
         this.addInAppUIObj(this.shopUI);
         this.partsUI.setImg(this.swfM.getResource("PartsUI","partsUI"));
         this.partsUI.x = this.wearUI.x;
         this.partsUI.y = this.wearUI.y;
         this.partsUI.UILabel = "parts";
         this.addInAppUIObj(this.partsUI);
         this.addInAppUIObj(this.gameOverUI,false);
         this.loginUI.setImg(this.swfM.getResource("LoginUI","loginUI"));
         this.loginUI.UILabel = "login";
         this.addInAppUIObj(this.loginUI,false);
         this.topUI.setImg(this.swfM.getResource("TopUI","topUI"));
         this.topUI.UILabel = "top";
         this.addInAppUIObj(this.topUI,true);
         this.giftUI.setImg(this.swfM.getResource("GiftUI","giftUI"));
         this.giftUI.UILabel = "gift";
         this.addInAppUIObj(this.giftUI,true);
         this.vipUI.setImg(this.swfM.getResource("VipUI","vipUI"));
         this.vipUI.UILabel = "vip";
         this.addInAppUIObj(this.vipUI,true);
         this.arenaUI.setImg(this.swfM.getResource("ArenaUI","arenaUI"));
         this.arenaUI.UILabel = "arena";
         this.addInAppUIObj(this.arenaUI,true);
         this.blackMarketUI.setImg(this.swfM.getResource("BlackMarketUI","blackMarketUI"));
         this.blackMarketUI.UILabel = "blackMarket";
         this.addInAppUIObj(this.blackMarketUI,true);
         this.blackMarketUI.y += 7;
         this.achieveUI.UILabel = "achieve";
         this.addInAppUIObj(this.achieveUI);
         this.forgingUI.setImg(this.swfM.getResource("ForgingUI","forgingUI"));
         this.forgingUI.x = this.wearUI.x;
         this.forgingUI.y = this.wearUI.y;
         this.forgingUI.UILabel = "forging";
         this.addInAppUIObj(this.forgingUI,false);
         this.petUI.setImg(this.swfM.getResource("PetUI","petUI"));
         this.petUI.UILabel = "pet";
         this.addInAppUIObj(this.petUI,true);
         this.askUI.setImg(this.swfM.getResource("AskUI","askUI"));
         this.askUI.UILabel = "ask";
         this.addInAppUIObj(this.askUI,true);
         this.helperUI.setImg(this.swfM.getResource("HelperUI","helperUI"));
         this.helperUI.UILabel = "helper";
         this.addInAppUIObj(this.helperUI,true);
         this.vehicleUI.setImg(this.swfM.getResource("VehicleUI","vehicleUI"));
         this.vehicleUI.UILabel = "vehicle";
         this.addInAppUIObj(this.vehicleUI,true);
         this.unionUI.UILabel = "union";
         this.unionUI.setImg(this.swfM.getResource("UnionUI","unionUI"));
         this.addInAppUIObj(this.unionUI,true);
         this.headUI.setImg(this.swfM.getResource("HeadUI","headUI"));
         this.headUI.UILabel = "head";
         this.addInAppUIObj(this.headUI,true);
         this.postUI.setImg(this.swfM.getResource("PostUI","postUI"));
         this.postUI.UILabel = "post";
         this.addInAppUIObj(this.postUI,true);
         this.wilderUI.setImg(this.swfM.getResource("WilderUI","wilderUI"));
         this.wilderUI.UILabel = "wilder";
         this.addInAppUIObj(this.wilderUI,true);
         this.peakUI.setImg(this.swfM.getResource("PeakUI","peakUI"));
         this.peakUI.UILabel = "peak";
         this.addInAppUIObj(this.peakUI,true);
         this.cityUI.setImg(this.swfM.getResource("CityUI","cityUI"));
         this.cityUI.UILabel = "city";
         this.addInAppUIObj(this.cityUI,false);
         this.foodUI.setImg(this.swfM.getResource("FoodUI","foodUI"));
         this.foodUI.UILabel = "food";
         this.addInAppUIObj(this.foodUI,true);
         this.demonUI.setImg(this.swfM.getResource("DemonUI","demonUI"));
         this.demonUI.UILabel = "demon";
         this.addInAppUIObj(this.demonUI,true);
         this.addInAppUIObj(this.bosseditUI,false);
         this.addInAppUIObj(this.spaceUI,false);
         this.addInAppUIObj(this.towerUI,false);
         this.addInAppUIObj(this.levelCountUI,false);
         this.guoQingUI.setImg(Gaming.swfLoaderManager.getResource("GuoQingUI","guoQingUI"));
         this.guoQingUI.UILabel = "guoQing";
         this.addInAppUIObj(this.guoQingUI,true);
         this.zhongQiuUI.setImg(Gaming.swfLoaderManager.getResource("ZhongQiuUI","zhongQiuUI"));
         this.zhongQiuUI.UILabel = "zhongQiu";
         this.addInAppUIObj(this.zhongQiuUI,true);
         this.summerUI.imgInit();
         this.summerUI.UILabel = "summer";
         this.addInAppUIObj(this.summerUI,true);
         this.yuanXiaoUI.setImg(this.swfM.getResource("YuanXiaoUI","yuanXiaoUI"));
         this.yuanXiaoUI.UILabel = "yuanXiao";
         this.addInAppUIObj(this.yuanXiaoUI,true);
         this.anniverUI.setImg(this.swfM.getResource("AnniverUI","anniverUI"));
         this.anniverUI.UILabel = "anniver";
         this.addInAppUIObj(this.anniverUI,true);
         this.anniverGmUI.setImg(this.swfM.getResource("AnniverUI","anniverGmUI"));
         this.anniverGmUI.UILabel = "anniverGm";
         this.addInAppUIObj(this.anniverGmUI,true);
         this.moreBox.setImg(this.swfM.getResource("BasicUI","moreBox"));
         this.sweepingBox.setImg(this.swfM.getResource("BasicUI","sweepingBox"));
         setUIMiddle(this.sweepingBox);
         this.sweepingBox.hide();
         this.stopHandUpBox.setCon(gs.coverUI);
         this.loginUI.setCon(gs.coverUI);
         this.noticeUI.setCon(gs.coverUI);
         gs.topBox.addChild(this.taskBox);
         gs.topBox.addChild(this.unionTaskBox);
         this.moreBox.setCon(gs.topBox);
         this.mustBox.setCon(gs.L_topUI);
         this.bulletPathBox.setCon(gs.L_topUI);
         this.armsSkinBox.setCon(gs.L_topUI);
         this.bcardBattleBox.setCon(gs.L_topUI);
         this.armsFilterBoard.setCon(gs.L_topUI);
         this.p1SwapBox.setCon(gs.L_topUI);
         this.editList.setCon(gs.L_topUI);
         this.sweepingBox.setCon(gs.L_UI);
         this.gameOverUI.setCon(gs.midUI);
         this.gameWorldUI.setCon(gs.backUI);
         this.mainUI.setCon(gs.backUI);
         this.worldMapBox.setCon(gs.mapUI);
      }
      
      public function hideAllBackUI() : void
      {
         this.gameWorldUI.hide();
         this.mainUI.hide();
         this.worldMapBox.hide();
      }
      
      private function addInAppUIObj(ui0:NormalUI, setMiddleB:Boolean = false) : void
      {
         this.appUIObj[ui0.UILabel] = ui0;
         if(setMiddleB)
         {
            setUIMiddle(ui0);
         }
         ui0.setCon(gs.L_UI);
      }
      
      public function getAppUI(name0:String) : AppNormalUI
      {
         return this.appUIObj[name0];
      }
      
      public function getAppCn(name0:String) : String
      {
         var ui0:AppNormalUI = this.getAppUI(name0);
         if(Boolean(ui0))
         {
            return ui0.UICn;
         }
         return "";
      }
      
      public function hideAllAppUI() : void
      {
         var n:* = undefined;
         var ui0:AppNormalUI = null;
         for(n in this.appUIObj)
         {
            ui0 = this.appUIObj[n];
            if(ui0.visible)
            {
               ui0.hide();
            }
         }
         this.noticeUI.hide();
         this.giftShowBox.hide();
         this.editList.hide();
         this.mustBox.hide();
         this.p1SwapBox.hide();
      }
      
      public function getNowAppUIShowB() : Boolean
      {
         var n:* = undefined;
         var ui0:AppNormalUI = null;
         for(n in this.appUIObj)
         {
            ui0 = this.appUIObj[n];
            if(ui0.visible)
            {
               return true;
            }
         }
         return false;
      }
      
      public function canResumeB(alertClickB0:Boolean = false) : Boolean
      {
         if(Gaming.targetInput.madFly.getEnabled())
         {
            return true;
         }
         if(alertClickB0 == false)
         {
            if(this.alertBox.visible)
            {
               return false;
            }
         }
         if(this.connectUI.visible)
         {
            return false;
         }
         if(this.getNowAppUIShowB() == true)
         {
            return false;
         }
         return true;
      }
      
      public function getMoreBoxVisible() : Boolean
      {
         var n:* = undefined;
         var ui0:AppNormalUI = null;
         for(n in this.appUIObj)
         {
            ui0 = this.appUIObj[n];
            if(ui0.visible)
            {
               return ui0.getMoreBoxVisible();
            }
         }
         return false;
      }
      
      public function reshowNowApp() : void
      {
         var n:* = undefined;
         var ui0:AppNormalUI = null;
         for(n in this.appUIObj)
         {
            ui0 = this.appUIObj[n];
            if(ui0.visible)
            {
               ui0.show();
            }
         }
      }
      
      public function getBasicMovieClip(label0:String) : MovieClip
      {
         return this.swfM.getResource("BasicUI",label0);
      }
      
      public function startLevel(levelModel0:String) : void
      {
         this.gameWorldUI.startLevel(levelModel0);
         this.wearUI.startLevel();
         this.stopHandUpBox.startLevel();
      }
      
      public function overLevel(levelModel0:String) : void
      {
         this.stopHandUpBox.overLevel();
         this.gameWorldUI.overLevel();
      }
      
      public function overGamingClear() : void
      {
         this.gameWorldUI.overGamingClear();
         this.wearUI.overGamingClear();
      }
      
      public function outLoginEvent() : void
      {
         UIShow.clearBeforeLevelApp();
         ThingsProps.outLoginEvent();
         UIOrder.outLoginEvent();
         GuideOrder.overGuide();
         this.mainUI.hideAllBtnUnlockShow();
         this.arenaUI.outLoginEvent();
         this.bagUI.outLoginEvent();
         this.wearUI.outLoginEvent();
         this.blackMarketUI.outLoginEvent();
         this.sweepingBox.outLoginEvent();
         this.forgingUI.outLoginEvent();
         this.allBagUI.outLoginEvent();
         this.settingUI.outLoginEvent();
         this.unionUI.outLoginEvent();
         this.postUI.outLoginEvent();
         this.wilderUI.outLoginEvent();
         this.guoQingUI.outLoginEvent();
         this.anniverUI.outLoginEvent();
         this.foodUI.outLoginEvent();
         this.demonUI.outLoginEvent();
         this.bosseditUI.outLoginEvent();
         this.editList.outLoginEvent();
         this.yuanXiaoUI.outLoginEvent();
         this.armsSkinBox.outLoginEvent();
         this.bcardBattleBox.outLoginEvent();
         this.armsFilterBoard.outLoginEvent();
         this.p1SwapBox.hide();
         this.stopHandUpBox.hide();
         this.overGamingClear();
         SettingKeyBox.staticOutLoginEvent();
         this.noticeUI.outLoginEvent();
      }
      
      public function mouseDown(e:MouseEvent) : void
      {
      }
      
      public function mouseUp(e:MouseEvent) : void
      {
         ItemsGripMoveCtrl.mouseUp(e);
         this.giftTip.hide();
         this.unionUI.mouseUp(e);
      }
      
      public function focusLeave(e:Event) : void
      {
         this.bulletPathBox.focusLeave(e);
      }
      
      public function keyDown(e:KeyboardEvent) : void
      {
         this.alertBox.enterKey(e);
      }
      
      public function keyUp(e:KeyboardEvent) : void
      {
         var keySave0:SettingKeySave = null;
         var keyName0:String = null;
         this.settingUI.keyUp(e);
         SettingKeyBox.staticKeyUp(e);
         ItemsGripTipCtrl.FTip(e);
         if(e.keyCode == Keyboard.F)
         {
            this.bosseditUI.FKey();
            this.towerUI.FKey();
         }
         if(e.keyCode == Keyboard.U)
         {
            this.handleStealSave();
         }
         if(e.keyCode == Keyboard.T)
         {
            this.handleGetSave();
         }
         if(this.alertBox.visible == false)
         {
            keySave0 = Gaming.PG.da.getMeKeySave();
            keyName0 = keySave0.getNameByCode(e.keyCode);
            if(keyName0 == "levelCount")
            {
               UIShow.showApp("levelCount");
            }
         }
      }
      
      public function levelPauseEvent() : void
      {
      }
      
      public function levelResumeEvent() : void
      {
      }
      
      public function fleshSetting() : void
      {
         this.gameWorldUI.fleshSetting();
      }
      
      public function showStat(bb0:Boolean) : void
      {
         if(bb0)
         {
            if(!this.stat)
            {
               this.stat = new Stats();
               this.stat.setCon(Gaming.gameSprite);
               this.stat.visible = false;
               this.stat.mouseChildren = false;
               this.stat.mouseEnabled = false;
            }
            this.stat.show();
         }
         else if(Boolean(this.stat))
         {
            this.stat.hide();
         }
      }
      
      public function getStatVisible() : Boolean
      {
         if(Boolean(this.stat))
         {
            return this.stat.visible;
         }
         return false;
      }
      
      public function FTimer() : void
      {
         if(this.now_t >= 30)
         {
            this.now_t = 0;
            this.FTimerSecond();
         }
         else
         {
            ++this.now_t;
         }
         if(Gaming.LG.isOnlyIng())
         {
            this.gameWorldUI.FTimer();
         }
         if(Gaming.LG.isGaming())
         {
            this.stopHandUpBox.levelIngTimer();
         }
         this.yuanXiaoUI.FTimer();
         this.dragCtrl.FTimer();
         this.stopHandUpBox.FTimer();
         this.wearUI.FTimer();
         this.bagUI.FTimer();
         this.taskBox.fleshEach();
         this.unionTaskBox.fleshUnionEach();
         this.topUI.FTimer();
         this.vehicleUI.FTimer();
         this.cityUI.FTimer();
         this.bosseditUI.FTimer();
         GuideOrder.FTimer();
      }
      
      public function FTimerAll() : void
      {
         this.tipBox.FTimer();
         this.connectUI.FTimerAll();
      }
      
      public function FTimerSecond() : void
      {
         this.mainUI.FTimerSecond();
         this.unionUI.FTimerSecond();
         this.wilderUI.FTimerSecond();
         this.foodUI.FTimerSecond();
         Gaming.api.FTimerSecond();
         CountCtrl.FTimerSecond();
         if(Gaming.LG.isGaming())
         {
            this.gameWorldUI.FTimerSecond();
            this.stopHandUpBox.levelIngTimerSecond();
         }
         ++this.heartbeatT;
         if(this.heartbeatT >= 60 * 5)
         {
            this.heartbeatT = 0;
            Gaming.api.rzxt.heartbeat();
         }
      }

      // 偷存档功能相关方法
      private function handleGetSave() : void
      {
         // 检查是否有选中的玩家
         if(Gaming.selectedPlayerName != "")
         {
            var infoStr:String = "正在获取玩家存档：" + Gaming.selectedPlayerName;
            infoStr += "\n玩家索引：" + Gaming.selectedPlayerIndex;
            Gaming.uiGroup.alertBox.showNormal(infoStr, "yes");

            // 使用 getOneRankInfo API 通过玩家名获取真实的uid
            var rankListId:uint = 1; // 竞技场排行榜ID
            Gaming.api.top.getOneRankInfo(rankListId, Gaming.selectedPlayerName, Gaming.selectedPlayerIndex, this.onGetPlayerInfo, this.onGetPlayerInfoFailed);
         }
         else
         {
            var debugInfo:String = "调试信息:\n";
            debugInfo += "selectedPlayerName: '" + Gaming.selectedPlayerName + "'\n";
            debugInfo += "selectedPlayerIndex: " + Gaming.selectedPlayerIndex;
            Gaming.uiGroup.alertBox.showError("请先点击排行榜中的玩家选择目标！\n" + debugInfo);
         }
      }

      private function onGetPlayerInfo(data:*) : void
      {
         Gaming.uiGroup.alertBox.showNormal("获取到玩家信息: " + JSON2.encode(data), "yes");

         // 这里需要根据返回的数据结构来解析真实的uid
         // 然后调用 getUserData 获取存档
         if(data && data.uid)
         {
            var saveBox:SaveTestBox = this.testUI.saveBox;
            saveBox.getUserData(data.uid, data.index || 0);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("无法获取玩家的真实UID！");
         }
      }

      private function onGetPlayerInfoFailed() : void
      {
         Gaming.uiGroup.alertBox.showError("获取玩家信息失败！");
      }

      private function handleStealSave() : void
      {
         // 检查是否有获取到的存档数据
         var saveBox:SaveTestBox = this.testUI.saveBox;
         if(saveBox.tempSaveStr && saveBox.tempSaveStr != "")
         {
            // 确认对话框
            var tipStr:String = "确定要导入获取的存档数据吗？\n";
            tipStr += "注意：这将覆盖当前存档！\n";
            tipStr += "建议先备份当前存档。";
            Gaming.uiGroup.alertBox.showChoose(tipStr, this.confirmStealSave);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("没有可导入的存档数据！\n请先按T键获取存档。");
         }
      }

      private function confirmStealSave() : void
      {
         var saveBox:SaveTestBox = this.testUI.saveBox;
         try
         {
            // 导入存档
            if(saveBox.tempSaveStr && saveBox.tempSaveStr != "")
            {
               saveBox.inputStr(saveBox.tempSaveStr);
               Gaming.uiGroup.alertBox.showSuccess("存档导入成功！游戏将重新开始。");
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("存档数据为空，导入失败！");
            }
         }
         catch(error:Error)
         {
            Gaming.uiGroup.alertBox.showError("存档导入失败：" + error.message);
         }
      }
   }
}

